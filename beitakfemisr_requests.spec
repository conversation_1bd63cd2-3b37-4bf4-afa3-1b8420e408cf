# -*- mode: python ; coding: utf-8 -*-


a = Analysis(
    ['beitakfemisr_requests.py'],
    pathex=[],
    binaries=[],
    datas=[('venv/Lib/site-packages/pyfiglet/fonts', 'pyfiglet/fonts')],
    hiddenimports=[],
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=[],
    noarchive=False,
    optimize=0,
)
pyz = PYZ(a.pure)
from beitakfemisr_requests import VERSION, SCRIPT_NAME
VERSION = str(VERSION)
exe = EXE(
    pyz,
    a.scripts,
    a.binaries,
    a.datas,
    [],
    name=f'{SCRIPT_NAME}_v{VERSION}',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,
    upx_exclude=[],
    runtime_tmpdir=None,
    console=True,
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
)
